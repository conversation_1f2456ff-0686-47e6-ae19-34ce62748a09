import 'dart:async';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/profile_provider.dart';
import 'package:flutter_audio_room/features/chat/data/conversation/conversation_local_datasource.dart';
import 'package:flutter_audio_room/features/chat/domain/interface/conversation_datasource.dart';
import 'package:flutter_audio_room/features/chat/domain/models/offline_conversation_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/remote_message_type.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/repository/signal_repository_provider.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_message_provider.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_protocol_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../domain/models/conversation_model.dart';

part 'conversation_provider.g.dart';

@riverpod
class Conversation extends _$Conversation {
  late ConversationDataSource dataSource;
  static const int pageSize = 20;

  @override
  Future<List<ConversationModel>> build() async {
    LogUtils.d('start build', tag: 'conversation provider');
    final userId = ref.read(accountProvider).userInfo?.profile?.id;
    dataSource = ConversationLocalDatasource(userId: userId ?? '');

    ref.onDispose(() {
      dataSource.dispose();
      ref.invalidate(chatSocketProvider);
      ref.invalidate(chatProvider);
    });

    return await _loadConversations(0, pageSize);
  }

  /// 加载会话数据，支持分页
  /// [page] 从0开始的页码
  /// [limit] 每页大小
  Future<List<ConversationModel>> _loadConversations(
      int offset, int limit) async {
    final result = await dataSource.getConversations(
      offset: offset,
      limit: limit,
    );
    
    return result.fold(
      (l) {
        LogUtils.e('Error fetching conversations: $l',
            tag: 'ConversationProvider');
        return [];
      },
      (r) {
        // 按置顶状态和更新时间排序
        r.sort((a, b) {
          // 首先按置顶状态排序
          if (a.pinned != b.pinned) {
            return a.pinned ? -1 : 1; // 置顶的排在前面
          }
          // 然后按更新时间排序
          return b.updatedAt.compareTo(a.updatedAt);
        });
        LogUtils.d('conversation build success', tag: 'conversation provider');
        return r;
      },
    );
  }

  /// 刷新会话列表，加载第一页数据
  Future<List<ConversationModel>> refresh() async {
    final conversations = await _loadConversations(0, pageSize);
    state = AsyncValue.data(conversations);
    return conversations;
  }

  /// 加载更多会话
  /// [currentConversations] 当前已加载的会话列表
  Future<List<ConversationModel>> loadMore(
      List<ConversationModel> currentConversations) async {
    final nextOffset = currentConversations.length;
    final newConversations = await _loadConversations(nextOffset, pageSize);

    if (newConversations.isEmpty) {
      // 没有更多数据了
      return currentConversations;
    }

    // 合并新旧数据并排序
    final allConversations = [...currentConversations, ...newConversations];

    // 按置顶状态和更新时间排序
    allConversations.sort((a, b) {
      // 首先按置顶状态排序
      if (a.pinned != b.pinned) {
        return a.pinned ? -1 : 1; // 置顶的排在前面
      }
      // 然后按更新时间排序
      return b.updatedAt.compareTo(a.updatedAt);
    });

    state = AsyncValue.data(allConversations);
    return allConversations;
  }

  Future<void> initSignal({bool forceUpdate = false}) async {
    final userId = ref.read(accountProvider).userInfo?.profile?.id;

    if (userId == null) {
      return;
    }

    final signalMessage = ref.read(signalMessageProvider.notifier);

    try {
      await signalMessage.initializeUser(userId);
      final status = await signalMessage.checkKeyStatus(userId);

      // 检查是否需要更新密钥
      final remoteKeyCount = await _getPreKeyCount();

      if (remoteKeyCount == 0 || forceUpdate || status.needsUpdate) {
        await _updateSignalKeys(userId, signalMessage, status, remoteKeyCount);
      } else {
        LogUtils.d('无需更新密钥', tag: 'ImConversationProvider');
      }
    } catch (e) {
      LogUtils.e('Signal 协议初始化失败: $e', tag: 'ImConversationProvider');
    }
  }

  /// 检查是否需要更新预密钥
  ///
  /// 返回true表示需要更新，false表示不需要更新
  Future<int> _getPreKeyCount() async {
    final deviceId = getIt<IDeviceInfoService>().deviceId;
    final keyCountResult = await ref
        .read(signalRepositoryProvider.notifier)
        .getPreKeysCount(deviceId);
    LogUtils.d('keyCountResult: ${keyCountResult.getRight()}',
        tag: 'ImConversationProvider');

    final keyCount = keyCountResult.getRight();
    return keyCount ?? 0;
  }

  /// 更新Signal密钥
  ///
  /// [userId] 用户ID
  /// [signalMessage] Signal消息服务
  Future<void> _updateSignalKeys(
    String userId,
    SignalMessage signalMessage,
    PreKeyStatus status,
    int remoteKeyCount,
  ) async {
    final newBundle = await signalMessage.updateKeys(userId, status: status);
    if (newBundle != null) {
      LogUtils.d('更新密钥包: ${newBundle.toString()}', tag: 'conversationProvider');

      final identityKey = newBundle['identityKey'] ?? '';
      final signedPreKey = newBundle['signedPreKey'] ?? '';
      final registrationId = newBundle['registrationId'] ?? 0;
      final preKeys = newBundle['preKeys'] ?? [];

      if (signedPreKey.isNotEmpty) {
        // 使用新的预密钥包更新用户信息
        await ref.read(signalRepositoryProvider.notifier).updateIdentityKey(
              identityKey: identityKey,
              signedPreKey: signedPreKey,
              registrationId: registrationId,
            );
      }

      if (preKeys.isNotEmpty &&
          (status.needsMorePreKeys || remoteKeyCount < 10)) {
        final deviceId = getIt<IDeviceInfoService>().deviceId;
        await ref.read(signalRepositoryProvider.notifier).addPreKeys(
              preKeys: preKeys,
              deviceId: deviceId,
            );
      }
    }
  }

  Future<ResultWithData<ConversationModel>> createConversation(
    String conversationId, {
    String? username,
    String? avatar,
  }) async {
    final coreService = ref.read(coreServiceProvider);
    final conversations = await future;

    final completer = Completer<OfflineConversationModel>();

    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      return Right(conversations[index]);
    }

    final ephemeralTimeout =
        coreService.config.chatConfigResp.msgClientTimeoutSeconds;

    await ref.read(chatSocketProvider.notifier).emitWithAck(
      ChatSocketEvents.conversationInfo,
      {
        'conversationId': conversationId,
        'msgType': RemoteMessageType.USER.type,
      },
      (ack) {
        try {
          final conversation = OfflineConversationModel.fromJson(ack['data']);
          LogUtils.d(conversation.toJson().toString(),
              tag: 'conversationProvider.createConversation');
          completer.complete(conversation);
        } catch (e) {
          LogUtils.e('Error completing conversation: $e',
              tag: 'conversationProvider.createConversation');
          completer.completeError(e);
        }
      },
    );

    var conversation = ConversationModel(
      id: conversationId,
      peer: types.User(
        id: conversationId,
        firstName: username,
        imageUrl: avatar,
        createdAt: DateTime.now().millisecondsSinceEpoch,
      ),
      ephemeralTimeout: ephemeralTimeout,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
    );

    try {
      final result = await completer.future;
      conversation = conversation.copyWith(
        ephemeralTimeout: result.conversationSetting?.msgClientTimeoutSeconds ??
            conversation.ephemeralTimeout,
        extend: result.extend,
        updatedAt: result.lastMsgTs ?? DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      LogUtils.e('Error completing conversation: $e',
          tag: 'conversationProvider.createConversation');
    }

    conversations.add(conversation);
    state = AsyncValue.data(conversations);

    return await dataSource.createConversation(conversation);
  }

  Future<ResultWithData<ConversationModel>> getOrCreateConversation(
    String conversationId, {
    String? username,
    String? avatar,
  }) async {
    final profileNotifier = ref.read(profileRepositoryProvider.notifier);
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      return Right(conversations[index]);
    } else {
      final conversation = await dataSource.getConversation(conversationId);
      if (conversation.isRight()) {
        return Right(conversation.getRight()!);
      }

      if (username == null || avatar == null) {
        final userInfo = await profileNotifier.getUserInfoById(conversationId);

        if (userInfo.isRight()) {
          username = userInfo.getRight()?.profile?.nickName;
          avatar = userInfo.getRight()?.profile?.avatar;
        }
      }

      return createConversation(
        conversationId,
        username: username,
        avatar: avatar,
      );
    }
  }

  Future<VoidResult> updateConversation(ConversationModel conversation) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversation.id);
    final conversationToUpdate = conversation.copyWith(
      updatedAt: DateTime.now().millisecondsSinceEpoch,
    );
    if (index >= 0) {
      // 更新现有会话
      conversations[index] = conversationToUpdate;
    } else {
      // 添加新会话
      conversations.add(conversationToUpdate);
    }
    // 按置顶状态和更新时间排序
    conversations.sort((a, b) {
      // 首先按置顶状态排序
      if (a.pinned != b.pinned) {
        return a.pinned ? -1 : 1; // 置顶的排在前面
      }
      // 然后按更新时间排序
      return b.updatedAt.compareTo(a.updatedAt);
    });
    state = AsyncValue.data(conversations);

    return await dataSource.updateConversation(conversationToUpdate);
  }

  Future<void> updateLastMessage(
    String conversationId,
    types.Message? message,
  ) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      final conversation = conversations[index];
      if (conversation.lastMessage?.id == message?.id) {
        return;
      }

      final newConversation = conversation.copyWith(lastMessage: message);
      conversations[index] = newConversation;
      state = AsyncValue.data(conversations);

      await dataSource.updateConversation(newConversation);
    }
  }

  Future<void> deleteConversationFromLocal(String conversationId) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      conversations.removeAt(index);
    }
    state = AsyncValue.data(conversations);

    await dataSource.deleteConversation(conversationId);

    await ref.read(chatProvider(conversationId).notifier).clearMessages();

    // await removeCryptSession(conversationId);
  }

  Future<void> syncOfflineConversations(
    int startTimeStamp, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.syncOfflineBasic,
          {'startTimeStamp': startTimeStamp},
          ack,
        );
  }

  Future<void> deleteConversations(
    List<String> conversationIds, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.deleteConversations,
          {'conversationIds': conversationIds},
          ack,
        );
    for (final conversationId in conversationIds) {
      await deleteConversationFromLocal(conversationId);
    }
  }

  Future<void> muteConversation(
    ConversationModel conversation, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.muteConversation,
          {'conversationId': conversation.id},
          ack,
        );
    await updateConversation(conversation.copyWith(muted: true));
  }

  Future<void> unmuteConversation(
    ConversationModel conversation, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.unmuteConversation,
          {'conversationId': conversation.id},
          ack,
        );
    await updateConversation(conversation.copyWith(muted: false));
  }

  Future<void> pinConversation(
    ConversationModel conversation, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.pinConversation,
          {'conversationId': conversation.id},
          ack,
        );
    await updateConversation(conversation.copyWith(pinned: true));
  }

  Future<void> unpinConversation(
    ConversationModel conversation, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.unpinConversation,
          {
            'conversationId': conversation.id,
          },
          ack,
        );
    await updateConversation(conversation.copyWith(pinned: false));
  }

  Future<void> updateConversationMsgTimeout(
    ConversationModel conversation, {
    required dynamic Function(dynamic) ack,
  }) async {
    await future;
    await ref.read(chatSocketProvider.notifier).emitWithAck(
          ChatSocketEvents.conversationMsgTimeoutUpdate,
          {
            'conversationId': conversation.id,
            'timeoutSeconds': conversation.ephemeralTimeout,
          },
          ack,
        );
    await updateConversation(conversation);
  }

  Future<void> clearNewDeviceTag(
    String conversationId, {
    dynamic Function(dynamic)? ack,
  }) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      final newConversation = conversations[index].copyWith(
        extend: conversations[index].extend.copyWith(
              dIdChanged: false,
            ),
      );
      conversations[index] = newConversation;
      state = AsyncValue.data(conversations);
    }
    await ref.read(chatSocketProvider.notifier).emitWithAck(
      ChatSocketEvents.conversationClearNewDeviceTag,
      {'conversationId': conversationId},
      (data) {
        ack?.call(data);
      },
    );
  }

  Future<void> setNeedRecreateSession(
    String conversationId, {
    required bool needRecreateSession,
  }) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      conversations[index] = conversations[index].copyWith(
        extend: conversations[index].extend.copyWith(
              dIdChanged: needRecreateSession,
            ),
      );
      state = AsyncValue.data(conversations);
      await updateConversation(conversations[index]);
    }
  }

  Future<int> getEphemeralTimeout(String conversationId) async {
    final conversations = await future;
    final index = conversations.indexWhere((c) => c.id == conversationId);
    if (index >= 0) {
      return conversations[index].ephemeralTimeout;
    }

    return ref.read(coreServiceProvider.select((state) {
      return state.config.chatConfigResp.msgClientTimeoutSeconds;
    }));
  }

  Future<void> removeCryptSession(String conversationId) async {
    final localUserId =
        ref.read(accountProvider).userInfo?.profile?.id;
    if (localUserId == null) {
      return;
    }
    await ref
        .read(signalMessageProvider.notifier)
        .deleteConversationSession(localUserId, conversationId);
  }

  Future<void> clearCryptSession() async {
    final localUserId =
        ref.read(accountProvider).userInfo?.profile?.id;
    if (localUserId == null) {
      return;
    }
    await ref
        .read(signalMessageProvider.notifier)
        .clearUserSession(localUserId);
  }
}

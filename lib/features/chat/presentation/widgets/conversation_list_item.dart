import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/string_ext.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/conversation_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/live_tag.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class ConversationListItem extends ConsumerWidget {
  const ConversationListItem({
    super.key,
    required this.conversation,
    required this.onTap,
    this.userOnlineStatus,
    this.userRoomStatus,
  });

  final ConversationModel conversation;
  final VoidCallback onTap;
  final UserOnlineStatusModel? userOnlineStatus;
  final UserRoomStatusModel? userRoomStatus;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final conversationUpdatedAt = conversation.updatedAt;
    final timeTs = conversation.lastMessage?.createdAt;
    final lastMessageTime =
        DateTime.fromMillisecondsSinceEpoch(timeTs ?? conversationUpdatedAt);
    final timeString = _formatTime(lastMessageTime);

    return SlidableAutoCloseBehavior(
      child: Slidable(
        key: ValueKey(conversation.id),
        endActionPane: ActionPane(
          motion: const DrawerMotion(),
          extentRatio: 0.25,
          children: [
            SlidableAction(
              onPressed: (_) => _handleDelete(context, ref),
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: 'Delete',
            ),
          ],
        ),
        startActionPane: ActionPane(
          extentRatio: 0.5,
          motion: const DrawerMotion(),
          children: [
            SlidableAction(
              onPressed: (_) => _handlePin(context, ref),
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.zero,
              icon: conversation.pinned
                  ? Icons.push_pin
                  : Icons.push_pin_outlined,
              label: conversation.pinned ? 'Unpin' : 'Pin',
            ),
            SlidableAction(
              onPressed: (_) => _handleMute(context, ref),
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.zero,
              icon: conversation.muted ? Icons.volume_up : Icons.volume_off,
              label: conversation.muted ? 'Unmute' : 'Mute',
            ),
          ],
        ),
        child: Container(
          color: conversation.pinned
              ? theme.colorScheme.surfaceContainerHighest
              : null,
          child: ListTile(
            onTap: onTap,
            leading: _buildAvatar(context),
            splashColor: Colors.transparent,
            title: Row(
              children: [
                Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: BoxDecoration(
                    color: userOnlineStatus?.isOnline == true
                        ? context.colorScheme.primary
                        : const Color(0xff939393),
                    shape: BoxShape.circle,
                  ),
                ),
                5.horizontalSpace,
                Expanded(
                  child: Text(
                    conversation.peer.firstName ?? 'Unknown User',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  timeString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 10.sp,
                    color: theme.textTheme.bodySmall?.color
                        ?.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
            subtitle: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      if (conversation.muted) ...[
                        Icon(
                          Icons.volume_off,
                          size: 16,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 4),
                      ],
                      Expanded(
                        child: Text(
                          _getLastMessageText(),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontSize: 10.sp,
                            color: theme.textTheme.bodyMedium?.color
                                ?.withValues(alpha: 0.6),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                if (conversation.unreadCount > 0)
                  Badge.count(count: conversation.unreadCount)
                else if (conversation.pinned)
                  Icon(
                    Icons.push_pin,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return LiveTagBuilder(
      userRoomStatus: userRoomStatus,
      builder: (context, onTap, liveTag) {
        return GestureDetector(
          onTap: () => onTap(),
          child: SizedBox(
            width: 35.w,
            height: 35.w,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                // 头像
                Positioned.fill(
                  child: AvatarWithFrame(
                    width: 35.w,
                    height: 35.w,
                    avatarUrl: conversation.peer.imageUrl ?? '',
                  ),
                ),

                if (userRoomStatus?.status == 1)
                  Positioned(
                    bottom: -5.h,
                    left: 0,
                    right: 0,
                    child: liveTag,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getLastMessageText() {
    if (conversation.lastMessage == null) {
      return 'No messages yet';
    }

    switch (conversation.lastMessage!.type) {
      case MessageType.text:
        final message = conversation.lastMessage as TextMessage;
        return message.text.length > 30
            ? '${message.text.substringSafe(0, 30)}...'
            : message.text;
      case MessageType.image:
        return '[Image]';
      case MessageType.audio:
        return '[Audio]';
      case MessageType.video:
        return '[Video]';
      case MessageType.system:
        final message = conversation.lastMessage as SystemMessage;
        if (message.extend.subtype == MessageSubtype.VOICE_CALL) {
          return '[Voice Call]';
        } else if (message.extend.subtype == MessageSubtype.INVITE_ROOM) {
          return '[Room Invite]';
        }
        
        return message.text.length > 30
            ? '${message.text.substringSafe(0, 30)}...'
            : message.text;
      default:
        return '[Text]';
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final timeDate = DateTime(time.year, time.month, time.day);
    final difference = today.difference(timeDate);

    // 如果是今天内的
    if (difference.inDays == 0) {
      // 显示 hh:mm
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }
    // 如果是一周内的
    else if (difference.inDays < 7) {
      // 显示周几
      final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      final weekday = time.weekday - 1; // 1-7 转换为 0-6
      return weekdays[weekday];
    }
    // 如果是今年内的
    else if (time.year == now.year) {
      // 显示 mm/dd
      final month = time.month.toString().padLeft(2, '0');
      final day = time.day.toString().padLeft(2, '0');
      return '$month/$day';
    }
    // 如果不是今年内的
    else {
      // 显示 yy/mm/dd
      final year = time.year.toString().substring(2); // 取年份的后两位
      final month = time.month.toString().padLeft(2, '0');
      final day = time.day.toString().padLeft(2, '0');
      return '$year/$month/$day';
    }
  }

  Future<void> _handleDelete(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content:
            const Text('Are you sure you want to delete this conversation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(conversationProvider.notifier).deleteConversations(
        [conversation.id],
        ack: (_) {},
      );
    }
  }

  Future<void> _handlePin(BuildContext context, WidgetRef ref) async {
    if (conversation.pinned) {
      await ref.read(conversationProvider.notifier).unpinConversation(
            conversation,
            ack: (_) {},
          );
    } else {
      await ref.read(conversationProvider.notifier).pinConversation(
            conversation,
            ack: (_) {},
          );
    }
  }

  Future<void> _handleMute(BuildContext context, WidgetRef ref) async {
    if (conversation.muted) {
      await ref.read(conversationProvider.notifier).unmuteConversation(
            conversation,
            ack: (_) {},
          );
    } else {
      await ref.read(conversationProvider.notifier).muteConversation(
            conversation,
            ack: (_) {},
          );
    }
  }
}

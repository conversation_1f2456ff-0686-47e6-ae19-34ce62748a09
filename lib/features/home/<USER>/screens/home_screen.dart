import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/leave_room_mixin.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/home_provider.dart';
import '../widgets/home_screen_bottom_bar.dart';
import '../widgets/home_screen_initializer.dart';
import '../widgets/home_screen_listeners.dart';
import '../widgets/home_screen_pages_builder.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin, LeaveRoomMixin, HomeScreenInitializer {
  @override
  void initState() {
    super.initState();
    init(ref);
  }

  @override
  void dispose() {
    disposeInitializer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(homeProvider).currentIndex;

    // Setup listeners
    HomeScreenListeners.setupListeners(context, ref, onLeaveRoom: () => leaveRoom());

    final pages = HomeScreenPagesBuilder.buildPages(context, ref);

    return Stack(
      children: [
        Positioned.fill(
          child: AppScaffold(
            contentPadding: EdgeInsets.zero,
            resizeToAvoidBottomInset: false,
            appBar: pages[currentIndex].appBar,
            body: IndexedStack(
              index: currentIndex,
              sizing: StackFit.expand,
              children: pages.map((e) => e.page).toList(),
            ),
            bottomNavigationBar: HomeScreenBottomBar(
              currentIndex: currentIndex,
              pages: pages,
            ),
          ),
        ),
      ],
    );
  }
}

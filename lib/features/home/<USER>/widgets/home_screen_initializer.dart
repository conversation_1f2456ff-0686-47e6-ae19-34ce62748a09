import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/app_lifecycle.dart' as app_lifecycle;
import 'package:flutter_audio_room/core/utils/firebase_message_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/local_notification_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_join_source.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/lucky_draw_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/audio_room_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/home/<USER>/providers/home_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/services/core_service/core_service.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_audio_room/services/location_service/i_location_service.dart';
import 'package:flutter_audio_room/services/network_info_service/i_network_info_service.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

mixin HomeScreenInitializer<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  // 用于标识生命周期回调的键
  static const String _lifecycleCallbackKey = 'home_screen';

  bool _notificationAuthorized = false;

  StreamSubscription<String>? _tokenRefreshSubscription;
  StreamSubscription<RemoteMessage>? _messageReceivedSubscription;

  void init(WidgetRef ref) {
    // 注册应用生命周期状态变化回调
    app_lifecycle.registerLifecycleCallback(
      _lifecycleCallbackKey,
      (context) {
        // 当应用从后台恢复到前台时
        if (context.currentState == AppLifecycleState.resumed &&
            context.isResumingFromBackground) {
          ref.read(homeProvider.notifier).setIsInBackground(false);
          // 使用原子操作重置WebSocket重连状态并检查连接，避免竞态条件
          getIt<WebSocketDataSourceManager>().resetAndCheckAllConnections();
          _clearNotificationAndBadge(ref);
          // 从后台恢复时重新初始化 Signal 协议，防止意外情况导致的状态不一致
          _initSignalProtocol(ref);
        }
        // 只有当应用进入真正的后台状态时才设置isInBackground为true
        else if (context.currentState == AppLifecycleState.paused) {
          ref.read(homeProvider.notifier).setIsInBackground(true);
        }
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (!mounted) {
        return;
      }
      final userInfo =
          ref.read(accountProvider.select((state) => state.userInfo));
      final hasUser = userInfo?.sessionInfo != null;
      if (!hasUser) {
        return;
      }
      _checkAndBackToRoom(ref);

      getIt<CoreService>().init();

      _initFirebaseMessage(ref);

      _updateInfo(ref);
      _initFriendWebSocket(ref);
      _initSignalProtocol(ref);
      _getMatchRemainQuota(ref);
      _initializeGiftList(ref);
      _initUserData(ref);
    });
  }

  void disposeInitializer() {
    _tokenRefreshSubscription?.cancel();
    _messageReceivedSubscription?.cancel();
    app_lifecycle.unregisterLifecycleCallback(_lifecycleCallbackKey);
  }

  Future<void> _initFirebaseMessage(WidgetRef ref) async {
    final authorized = await FirebaseMessageUtils.requestPermission();
    if (!authorized) return;

    _notificationAuthorized = authorized;

    final token = await FirebaseMessageUtils.getToken();
    LogUtils.i('FirebaseMessageUtils.getToken: $token', tag: 'App');
    if (!mounted) return;
    ref.read(coreServiceProvider.notifier).updateFcmToken(token);

    _tokenRefreshSubscription =
        FirebaseMessageUtils.tokenRefreshStream?.listen((token) {
      LogUtils.i('FirebaseMessageUtils.tokenRefreshSubscription: $token',
          tag: 'App');
      ref.read(coreServiceProvider.notifier).updateFcmToken(token);
    });

    _clearNotificationAndBadge(ref);

    _setupInteractedMessage(ref);
  }

  void _clearNotificationAndBadge(WidgetRef ref) {
    if (!_notificationAuthorized) return;
    NotificationService.clearAllNotificationsAndBadges();

    if (!mounted) return;
    ref.read(coreServiceProvider.notifier).clearNotificationCount();
  }

  Future<void> _setupInteractedMessage(WidgetRef ref) async {
    final message = await FirebaseMessageUtils.getInitialMessage();
    if (message != null) {
      _handleInteractedMessage(message);
    }

    _messageReceivedSubscription =
        FirebaseMessageUtils.messageReceivedStream?.listen((message) {
      _handleInteractedMessage(message);
    });
  }

  void _handleInteractedMessage(RemoteMessage message) {
    LogUtils.i(
      'FirebaseMessageUtils._handleInteractedMessage: ${message.toMap()}',
      tag: 'App',
    );
  }

  Future<void> _getMatchRemainQuota(WidgetRef ref) async {
    final coreServiceNotifier = ref.read(coreServiceProvider.notifier);
    await coreServiceNotifier.getMatchRemainQuota();
  }

  Future<void> _checkAndBackToRoom(WidgetRef ref) async {
    final user = ref.read(accountProvider.select((state) => state.userInfo));
    final audioRoomNotifier = ref.read(audioRoomProvider.notifier);
    await audioRoomNotifier.initialize();

    final roomInfo = audioRoomNotifier.checkLocalRoomInfo();
    if (roomInfo != null && user != null) {
      await LoadingUtils.showLoading();
      final result = await audioRoomNotifier.joinRoom(
        user: user,
        roomId: roomInfo.id ?? '',
        joinSource: RoomJoinSource.creator_reconnect,
      );
      result.fold(
        (failure) => LoadingUtils.showError(failure.message),
        (response) async {
          await LoadingUtils.dismiss();
          if (mounted) {
            context.push(
              RoutePageConfig(route: AudioRoomScreen.route()),
            );
          }
        },
      );
    }
  }

  Future<void> _initializeGiftList(WidgetRef ref) async {
    _initGiftList(ref);
    _initLuckyDrawList(ref);
  }

  Future<void> _initGiftList(WidgetRef ref) async {
    final giftStateNotifier = ref.read(giftStateNotifierProvider.notifier);
    await giftStateNotifier.refreshGiftList();
    await giftStateNotifier.refreshFrameList();
    await giftStateNotifier.refreshBagFrameList();
    await giftStateNotifier.getReceivedGiftHistory(
          current: 1,
          size: 10,
        );
    await giftStateNotifier.getSentGiftHistory(
          current: 1,
          size: 10,
        );
  }

  Future<void> _initLuckyDrawList(WidgetRef ref) async {
    final luckyDrawNotifier = ref.read(luckyDrawProvider.notifier);
    await luckyDrawNotifier.fetchDrawItems();
  }

  Future<void> _initFriendWebSocket(WidgetRef ref) async {
    final notifier = ref.read(friendVoiceCallProvider.notifier);
    await notifier.initializeWebSocket();
  }

  /// Initialize Signal protocol on app startup
  /// This ensures Signal is initialized even if the server notification is missed
  Future<void> _initSignalProtocol(WidgetRef ref) async {
    try {
      final conversationNotifier = ref.read(conversationProvider.notifier);
      await conversationNotifier.initSignal();
    } catch (e) {
      LogUtils.e('Signal 协议启动时初始化失败: $e', tag: 'HomeScreenInitializer');
    }
  }

  Future<void> _updateInfo(WidgetRef ref) async {
    _updateLocation(ref);
    _updateIpAddress(ref);
  }

  Future<void> _updateIpAddress(WidgetRef ref) async {
    final accountNotifier = ref.read(accountProvider.notifier);
    final result = await getIt<INetworkInfoService>().getPublicIpAddress();
    result.fold(
      (error) => LogUtils.e('Update ipAddress error: $error', tag: 'App'),
      (ipAddress) {
        if (!mounted) return;
        accountNotifier.updateIpAddress(ipAddress: ipAddress);
      },
    );
  }

  Future<void> _updateLocation(WidgetRef ref) async {
    final accountNotifier = ref.read(accountProvider.notifier);
    final result = await getIt<ILocationService>().getCurrentLocation();
    result.fold(
      (error) => LogUtils.e('location: $error', tag: 'App'),
      (location) {
        accountNotifier.updateLocation(
          countryCode: location.countryCode,
          cityName: location.cityName,
          latitude: location.latitude,
          longitude: location.longitude,
        );
      },
    );
  }

  Future<void> _initUserData(WidgetRef ref) async {
    final accountNotifier = ref.read(accountProvider.notifier);
    try {
      // Fetch subscription info and personal info
      await accountNotifier.refreshUserData();
    } catch (e) {
      LogUtils.e('Failed to initialize user data: $e', tag: 'HomeScreen');
    }
  }
}